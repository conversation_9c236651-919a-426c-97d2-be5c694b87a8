log:
  level: DEBUG

entryPoints:
  web:
    # http
    address: ':1080'
    http:
      # https://doc.traefik.io/traefik/routing/entrypoints/#entrypoint
      redirections:
        entryPoint:
          to: web-secure

  web-secure:
    # https
    address: ':1443'

  flower:
    address: ':1555'

certificatesResolvers:
  letsencrypt:
    # https://doc.traefik.io/traefik/https/acme/#lets-encrypt
    acme:
      email: '<EMAIL>'
      storage: /etc/traefik/acme/acme.json
      # https://doc.traefik.io/traefik/https/acme/#httpchallenge
      httpChallenge:
        entryPoint: web

http:
  routers:
    web-secure-router:
      rule: 'Host(`xkksgt.linghaoxin.com`)'
      entryPoints:
        - web-secure
      middlewares:
        - csrf
      service: nginx
      tls:
        # https://doc.traefik.io/traefik/routing/routers/#certresolver
        certResolver: letsencrypt

    flower-secure-router:
      rule: 'Host(`xkksgt.linghaoxin.com`)'
      entryPoints:
        - flower
      service: flower
      tls:
        # https://doc.traefik.io/traefik/master/routing/routers/#certresolver
        certResolver: letsencrypt

  middlewares:
    csrf:
      # https://doc.traefik.io/traefik/master/middlewares/http/headers/#hostsproxyheaders
      # https://docs.djangoproject.com/en/dev/ref/csrf/#ajax
      headers:
        hostsProxyHeaders: ['X-CSRFToken']

  services:
    nginx:
      loadBalancer:
        servers:
          - url: http://nginx:80

    flower:
      loadBalancer:
        servers:
          - url: http://flower:5555

providers:
  # https://doc.traefik.io/traefik/master/providers/file/
  file:
    filename: /etc/traefik/traefik.yml
    watch: true
