# 主站配置
server {
    server_name www.linghaoxin.com linghaoxin.com;

    root /var/www/html;
    index index.html;

    location / {
        try_files $uri $uri/ =404;
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/linghaoxin.com/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/linghaoxin.com/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

# CSP 配置
server {
    server_name xkksgt.linghaoxin.com;

    # proxy_connect_timeout 180s;
    # proxy_send_timeout 180s;
    # proxy_read_timeout 180s;

    location / {
        proxy_pass https://127.0.0.1:1443;
        proxy_pass_request_headers on;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # proxy_ssl_verify off;
    }

    client_max_body_size 300M;
    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/xkksgt.linghaoxin.com/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/xkksgt.linghaoxin.com/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}
server {
    if ($host = xkksgt.linghaoxin.com) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen 80;
    server_name xkksgt.linghaoxin.com;
    return 404; # managed by Certbot
}
server {
    if ($host = www.linghaoxin.com) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    if ($host = linghaoxin.com) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    server_name www.linghaoxin.com linghaoxin.com;
    listen 80;
    return 404; # managed by Certbot
}
