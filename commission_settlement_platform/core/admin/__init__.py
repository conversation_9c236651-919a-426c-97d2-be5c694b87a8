from django.contrib import admin

from commission_settlement_platform.core.admin.excel_file_admin import ExcelFileAdmin
from commission_settlement_platform.core.admin.marketing_account_admin import MarketingAccountAdmin
from commission_settlement_platform.core.admin.marketing_team_admin import MarketingTeamAdmin
from commission_settlement_platform.core.admin.order_admin import OrderAdmin
from commission_settlement_platform.core.admin.settle_work_order_admin import SettleWorkOrderAdmin
from commission_settlement_platform.core.admin.order_file_admin import OrderFileAdmin

from .actions import no_confirmation_delete_selected
from .forms import TurnstileAdminAuthenticationForm

admin.site.site_header = '熊卡快手隔天'
admin.site.site_title = '熊卡快手隔天'
admin.site.index_title = '熊卡快手隔天'

admin.site.disable_action("delete_selected")
admin.site.add_action(no_confirmation_delete_selected, "delete_selected")
admin.AdminSite.login_form = TurnstileAdminAuthenticationForm
