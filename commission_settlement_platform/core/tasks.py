from collections import defaultdict
from datetime import datetime
from decimal import Decimal

from celery import shared_task
from celery_progress.backend import ProgressRecorder
from django.core.exceptions import ImproperlyConfigured, ValidationError
from django.db import transaction
from django.db.models import Q
from django.utils import timezone

from commission_settlement_platform.core.excel_file_process import ExcelFileProcessor
from commission_settlement_platform.core.exceptions import ExcelTransError

try:
    from commission_settlement_platform.core.models import (
        OrderFile,
        OrderFilterFile,
        MarketingTeamMapFile,
        Order,
        MarketingTeam,
        MarketingAccount,
        SettleWorkOrder,
        concrete_fields,
    )
except ImproperlyConfigured:
    import os
    import django

    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
    django.setup()
    from commission_settlement_platform.core.models import (
        OrderFile,
        OrderFilterFile,
        MarketingTeamMapFile,
        Order,
        MarketingTeam,
        MarketingAccount,
        SettleWorkOrder,
        concrete_fields
    )


@shared_task(bind=True)
def import_order_file(self, pk):
    excel_file_processor = ExcelFileProcessor(
        model=OrderFile,
        pk=pk,
        task=self
    )

    # 表头相关
    order_h2f = {
        '订单号': 'order_id',
        '商品ID': 'product_id',
        '商品名称': 'product_name',
        '商品规格': 'product_specification',
        '商品单价': 'product_unit_price',
        '成交数量': 'deals_count',
        '实付款': 'order_actual_payment',
        '订单创建时间': 'order_created_dt',
        '订单支付时间': 'order_pay_dt',
        '订单状态': 'order_state',
        '退货退款': 'order_refunds_state',
        'SKU编码': 'settle_rate_percentage',
        '预估推广者分佣比例': 'commission_rate',
    }
    marketing_account_h2f = {
        'CPS达人ID': 'account_id',
        'CPS达人昵称': 'account_name',
    }
    entry_criteria_h2f = {
        '渠道': 'marketing_channel',
        '团长ID': 'captain_id'
    }
    order_c2h = excel_file_processor.h2f_trans2_c2h(order_h2f)
    marketing_account_c2h = excel_file_processor.h2f_trans2_c2h(marketing_account_h2f)
    entry_criteria_c2h = excel_file_processor.h2f_trans2_c2h(entry_criteria_h2f)

    # 检查必需的表头
    required_headers = set(order_h2f.keys()) | set(marketing_account_h2f.keys()) | set(entry_criteria_h2f.keys())
    sheet_headers = set(order_c2h.values()) | set(marketing_account_c2h.values()) | set(entry_criteria_c2h.values())
    missing_header = required_headers - sheet_headers
    if missing_header:
        raise ValueError(f'表格中缺少字段: {missing_header}')

    # 字段转换器
    order_field_converters = {
        'order_id': int,
        'product_id': int,
        'product_name': lambda x: x,
        'product_specification': lambda x: x,
        'deals_count': int,
        'settle_rate_percentage': int,
        'product_unit_price': lambda x: Decimal(str(x).replace('¥', '')),
        'order_actual_payment': lambda x: Decimal(str(x).replace('¥', '')),
        'order_created_dt': lambda x: timezone.make_aware(datetime.strptime(x, '%Y-%m-%d %H:%M:%S')),
        'order_pay_dt': lambda x: timezone.make_aware(datetime.strptime(x, '%Y-%m-%d %H:%M:%S')) if x else None,
        'order_state': lambda x: x,
        'order_refunds_state': lambda x: '' if x in ['-', None] else x,
        'commission_rate': lambda x: Decimal(str(x).replace('%', '')),
    }
    marketing_account_field_converters = {
        'account_id': int,
        'account_name': lambda x: x or '',
    }
    entry_criteria_field_converters = {
        'marketing_channel': lambda x: x,
        'captain_id': lambda x: x,
    }

    excel_file_processor.progress_recorder.set_progress(
        current=0, total=excel_file_processor.sheet_max_row, description="开始读取表格..."
    )

    be_imported_orders = {}
    marketing_account_map = {}

    for row in excel_file_processor.not_empty_iter_rows:
        try:
            entry_criteria_fields = excel_file_processor.trans_row_to_fields(
                row=row,
                h2f=entry_criteria_h2f,
                c2h=entry_criteria_c2h,
                field_converters=entry_criteria_field_converters,
            )
            if entry_criteria_fields['captain_id'] or (entry_criteria_fields['marketing_channel'] == '自营'):
                continue
            # 处理营销账号数据
            marketing_account_fields = excel_file_processor.trans_row_to_fields(
                row=row,
                h2f=marketing_account_h2f,
                c2h=marketing_account_c2h,
                field_converters=marketing_account_field_converters,
            )
            if all(marketing_account_fields.values()):
                marketing_account = marketing_account_map.get(marketing_account_fields['account_id'])
                if not marketing_account:
                    marketing_account, created = MarketingAccount.objects.update_or_create(
                        account_id=marketing_account_fields['account_id'],
                        defaults={
                            'account_name': marketing_account_fields['account_name']
                        }
                    )
                    marketing_account_map[marketing_account.account_id] = marketing_account
            else:
                marketing_account = None

            # 处理订单数据
            order_fields = excel_file_processor.trans_row_to_fields(
                row=row,
                h2f=order_h2f,
                c2h=order_c2h,
                field_converters=order_field_converters,
            )
            order_fields['marketing_account'] = marketing_account
            order_fields['src_file'] = excel_file_processor.model_obj
            be_imported_orders[order_fields['order_id']] = order_fields

        except ExcelTransError as e:
            excel_file_processor.model_obj.error_count += 1
            excel_file_processor.logger.error(f"{row[0].coordinate} 行 {e.excel_header} 转换错误: {e.value}")
        except ValidationError as e:
            excel_file_processor.model_obj.error_count += 1
            excel_file_processor.logger.error(f"{e}: {order_fields['order_id']}")
        finally:
            excel_file_processor.progress_recorder.increment_progress(description='正在读取表格...')

    excel_file_processor.progress_recorder.set_progress(
        current=0, total=len(be_imported_orders), description="开始写入数据..."
    )

    def order_generator():
        existed_orders = Order.objects.only('order_id', 'settle_state').in_bulk(
            be_imported_orders.keys(), field_name='order_id'
        )
        for _order_fields in be_imported_orders.values():
            _order = Order(**_order_fields)
            if existed_order := existed_orders.get(_order_fields['order_id']):
                _order.settle_state = existed_order.settle_state
            try:
                _order.pre_save()
                _order.clean_fields()
                yield _order
            except ValidationError as e:
                excel_file_processor.model_obj.error_count += 1
                excel_file_processor.logger.error(f"{e}: {_order_fields['order_id']}")
            finally:
                excel_file_processor.progress_recorder.increment_progress(description="正在写入数据...")

    created_orders = Order.objects.bulk_create(
        order_generator(),
        update_conflicts=True,
        update_fields=set(concrete_fields(Order)) - {
            'id',
            'settle_state',
            'approved_marketing_team_name',
            'full_settle_state',
            'settle_rate_decimal',
            'settle_amount',
            'settle_work_order',
        },
        unique_fields=['order_id'],
        batch_size=5000,
    )

    for created_order in created_orders:
        excel_file_processor.logger.success(created_order)

    excel_file_processor.model_obj.success_count = len(created_orders)
    excel_file_processor.model_obj.import_completed = True
    excel_file_processor.model_obj.save(apply_task=False)
    excel_file_processor.cleanup_logger()


@shared_task(bind=True)
def import_order_filter_file(self, pk):
    excel_file_processor = ExcelFileProcessor(
        model=OrderFilterFile,
        pk=pk,
        task=self
    )

    # 表头相关
    order_filter_h2f = {
        '订单id': 'order_id',
        '佣金率': 'commission_rate',
    }
    order_filter_c2h = excel_file_processor.h2f_trans2_c2h(order_filter_h2f)
    required_headers = set(order_filter_h2f.keys())
    sheet_headers = set(order_filter_c2h.values())
    missing_header = required_headers - sheet_headers
    if missing_header:
        raise ValueError(f'表格中缺少字段: {missing_header}')

    # 字段转换器
    order_filter_field_converters = {
        'order_id': int,
        'commission_rate': lambda x: Decimal(str(x).replace('%', '')),
    }

    excel_file_processor.progress_recorder.set_progress(
        current=0, total=excel_file_processor.sheet_max_row, description="开始读取表格..."
    )

    be_update_datas = {}

    for row in excel_file_processor.not_empty_iter_rows:
        try:
            order_filter_fields = excel_file_processor.trans_row_to_fields(
                row=row,
                h2f=order_filter_h2f,
                c2h=order_filter_c2h,
                field_converters=order_filter_field_converters,
            )
            order_id = order_filter_fields.pop('order_id')
            be_update_datas[order_id] = order_filter_fields
        except ExcelTransError as e:
            excel_file_processor.model_obj.error_count += 1
            excel_file_processor.logger.error(f"{row[0].coordinate} 行 {e.excel_header} 转换错误: {e.value}")
        except ValidationError as e:
            excel_file_processor.model_obj.error_count += 1
            excel_file_processor.logger.error(f"{e}: {order_filter_fields['order_id']}")
        finally:
            excel_file_processor.progress_recorder.increment_progress(description='正在读取表格...')

    # 查询已存在的 orders，用于后续更新字段
    existed_orders = Order.objects.in_bulk(
        be_update_datas.keys(), field_name='order_id'
    )

    excel_file_processor.progress_recorder.set_progress(
        current=0, total=len(be_update_datas), description="开始写入数据..."
    )

    for order_id, order_fields in be_update_datas.items():
        try:
            _order = existed_orders[order_id]
            for field_name, filed_value in order_fields.items():
                setattr(_order, field_name, filed_value)
            try:
                _order.pre_save()
                _order.clean_fields()
            except ValidationError as e:
                excel_file_processor.model_obj.error_count += 1
                excel_file_processor.logger.error(f"{e}: {order_id}")
            excel_file_processor.logger.success(f'{order_id}: {order_fields}')
        except KeyError:
            excel_file_processor.model_obj.error_count += 1
            excel_file_processor.logger.error(f'不存在该订单: {order_id}')
        finally:
            excel_file_processor.progress_recorder.increment_progress(description='正在写入数据...')

    excel_file_processor.model_obj.success_count = Order.objects.bulk_update(
        existed_orders.values(),
        fields=['commission_rate', 'settle_state'],
        batch_size=5000,
    )

    excel_file_processor.model_obj.import_completed = True
    excel_file_processor.model_obj.save(apply_task=False)
    excel_file_processor.cleanup_logger()


@shared_task(bind=True)
def import_marketing_team_map_file(self, pk):
    excel_file_processor = ExcelFileProcessor(
        model=MarketingTeamMapFile,
        pk=pk,
        task=self,
        required_header=False,
        iter_rows_kwargs={
            'max_col': 1
        }
    )

    be_created_datas = defaultdict(list)

    marketing_team_name = None

    marketing_account_team_map = {}

    excel_file_processor.progress_recorder.set_progress(
        current=0, total=excel_file_processor.sheet.max_row, description="开始读取表格..."
    )

    for row in excel_file_processor.not_empty_iter_rows:
        cell = row[0]
        if cell.value:
            cell_value = str(int(cell.value)) if cell.data_type == 'n' else str(cell.value).strip()
            if not cell_value.isdigit():
                marketing_team_name = cell_value
            else:
                if marketing_team_name is None:
                    raise ValidationError(
                        '首行账号没有匹配的名字'
                    )
                if cell_value in marketing_account_team_map:
                    excel_file_processor.model_obj.error_count += 1
                    excel_file_processor.logger.error(
                        f"{cell_value}({marketing_team_name}) 已存在于 {marketing_account_team_map[cell_value]} 中"
                    )
                else:
                    be_created_datas[marketing_team_name].append(cell_value)
                    marketing_account_team_map[cell_value] = marketing_team_name

            excel_file_processor.progress_recorder.set_progress(
                current=cell.row, total=excel_file_processor.sheet.max_row, description="开始读取表格..."
            )

    excel_file_processor.progress_recorder.set_progress(
        current=0, total=len(be_created_datas), description="开始创建营销团队..."
    )

    def marketing_team_generator():
        for team_name in be_created_datas.keys():
            try:
                marketing_team_instance = MarketingTeam(team_name=team_name)
                marketing_team_instance.clean_fields()
                yield marketing_team_instance
            except ValidationError as e:
                excel_file_processor.model_obj.error_count += 1
                excel_file_processor.logger.error(f"{e}: {team_name}")
            finally:
                excel_file_processor.progress_recorder.increment_progress(description="正在创建营销团队...")

    created_marketing_teams = MarketingTeam.objects.bulk_create(
        marketing_team_generator(),
        unique_fields=['team_name'],
        ignore_conflicts=True,
        batch_size=5000,
    )

    for marketing_team in created_marketing_teams:
        excel_file_processor.logger.success(f'已创建营销团队: {marketing_team.team_name}')

    marketing_team_map = MarketingTeam.objects.in_bulk(
        be_created_datas.keys(),
        field_name='team_name',
    )

    excel_file_processor.progress_recorder.set_progress(
        current=0,
        total=sum(len(account_ids) for account_ids in be_created_datas.values()),
        description="开始创建营销账号..."
    )

    def marketing_account_generator():
        for team_name, account_id_list in be_created_datas.items():
            for account_id in account_id_list:
                try:
                    marketing_account = MarketingAccount(
                        marketing_team=marketing_team_map[team_name],
                        account_id=account_id
                    )
                    marketing_account.clean_fields()
                    yield marketing_account
                except ValidationError as e:
                    excel_file_processor.model_obj.error_count += 1
                    excel_file_processor.logger.error(f"{e}: {team_name} - {account_id}")
                finally:
                    excel_file_processor.progress_recorder.increment_progress(description="正在创建营销账号...")

    created_marketing_accounts = MarketingAccount.objects.bulk_create(
        marketing_account_generator(),
        unique_fields=['account_id'],
        update_conflicts=True,
        update_fields=['marketing_team'],  # 不用覆盖 account_name
        batch_size=5000,
    )

    for created_marketing_account in created_marketing_accounts:
        excel_file_processor.logger.success(
            f'已创建营销账号: {created_marketing_account.marketing_team.team_name}-{created_marketing_account.account_id}'
        )

    excel_file_processor.progress_recorder.increment_progress(description='营销账号创建完成...')
    excel_file_processor.model_obj.success_count = len(created_marketing_accounts)
    excel_file_processor.model_obj.import_completed = True
    excel_file_processor.model_obj.save(apply_task=False)
    excel_file_processor.cleanup_logger()


@shared_task(bind=True)
def update_orders_when_approved(self, settle_work_order_id, requested_approved):
    """
    当结算工单被批准时，将其关联的订单状态更新为"已结算"，并在同一事务中更新工单状态

    Args:
        settle_work_order_id: 结算工单ID
        requested_approved: 请求的审批状态
    """
    try:
        with transaction.atomic():
            settle_work_order = SettleWorkOrder.objects.select_for_update().get(pk=settle_work_order_id)
            if not self.request.id:
                self.request.id = settle_work_order.task_id.bytes
            settle_work_order.task_id = self.request.id
            settle_work_order.save(update_fields=['task_id'])
            # 记录当前进度
            progress_recorder = ProgressRecorder(self)
        with transaction.atomic():
            settle_work_order = SettleWorkOrder.objects.select_for_update().get(pk=settle_work_order_id)
            instance_order_set = settle_work_order.order_set.select_related(
                'marketing_account__marketing_team'
            ).all()
            # instance_order_set = settle_work_order.order_set.select_related(
            #     'marketing_account__marketing_team'
            # ).filter(
            #     full_settle_state__in=['已结算', '未结算']
            # )

            # 在事务内使用 select_for_update 锁定工单记录
            order_set_approved = instance_order_set.filter(settle_state='已结算').exists()
            if order_set_approved == requested_approved:
                return (
                    f'状态没有变化，无需继续执行任务('
                    f'{"🟢已结算" if order_set_approved else "🟡结算中"} ➡ '
                    f'{"🟢已结算" if requested_approved else "🟡结算中"}'
                    f')'
                )

            if requested_approved:  # 当工单被批准时，检查是否存在未绑定营销团队或营销账号的订单
                not_marketing_bound_order_count = instance_order_set.filter(
                    Q(marketing_account__isnull=True) |
                    Q(marketing_account__marketing_team__isnull=True)
                ).count()

                if not_marketing_bound_order_count:
                    settle_work_order.approved = False
                    settle_work_order.save()
                    return f'❌ 有 {not_marketing_bound_order_count} 条未绑定营销团队或营销账号的订单，请先绑定'

            total_orders = len(instance_order_set)

            progress_recorder.set_progress(
                0, total_orders, f'开始处理订单...'
            )

            # 批处理计数
            processed_count = 0
            batch_size = 5000
            current_batch = []

            for i, order in enumerate(instance_order_set):
                if requested_approved:
                    order.settle_state = '已结算'
                    if order.marketing_account and order.marketing_account.marketing_team:
                        order.approved_marketing_team_name = order.marketing_account.marketing_team.team_name
                    else:
                        order.approved_marketing_team_name = ''
                else:
                    order.settle_state = '未结算'
                    order.approved_marketing_team_name = ''

                current_batch.append(order)
                processed_count += 1

                # 每 batch_size 条记录进行一次批量更新
                if len(current_batch) >= batch_size or i == total_orders - 1:
                    Order.objects.bulk_update(
                        current_batch,
                        fields=['settle_state', 'approved_marketing_team_name'],
                    )
                    progress_recorder.set_progress(
                        processed_count,
                        total_orders,
                        f'正在将订单状态更新为: {"🟢已结算" if requested_approved else "🟡结算中"}'
                    )
                    current_batch = []

            # 所有订单更新完成后，再更新工单状态
            settle_work_order.approved = requested_approved
            settle_work_order.approved_dt = timezone.now() if requested_approved else None
            settle_work_order.save()

        return (f'成功处理 {total_orders} 条订单，'
                f'订单状态已更新为: {"🟢已结算" if requested_approved else "🟡结算中"}，'
                f'请刷新页面查看最新数据')
    finally:
        # 确保在出现异常时也会重置处理状态
        with transaction.atomic():
            settle_work_order = SettleWorkOrder.objects.select_for_update().get(pk=settle_work_order_id)
            # settle_work_order.approved = requested_approved
            settle_work_order.save()


@shared_task(bind=True)
def associate_orders_to_settle_work_order(self, settle_work_order_id, requested_has_associated_orders):
    """
    将订单关联到结算工单的异步任务

    Args:
        settle_work_order_id: 结算工单ID
    """
    try:
        with transaction.atomic():
            settle_work_order = SettleWorkOrder.objects.select_for_update().get(pk=settle_work_order_id)
            if not self.request.id:
                self.request.id = settle_work_order.task_id.bytes
            settle_work_order.task_id = self.request.id
            settle_work_order.save(update_fields=['task_id'])
            # 记录当前进度
            progress_recorder = ProgressRecorder(self)
        with transaction.atomic():
            settle_work_order = SettleWorkOrder.objects.select_for_update().get(pk=settle_work_order_id)

            # 检查实际的订单关联状态
            has_orders = Order.objects.filter(settle_work_order=settle_work_order).exists()
            if has_orders:
                return '工单订单关联状态没有变化，无需更新'  # 状态没有变化，无需继续执行任务

            progress_recorder.set_progress(
                current=0, total=3, description="正在查询关联订单..."
            )
            order_filter_condiction = (
                Q(marketing_account__marketing_team__isnull=False) &
                Q(full_settle_state='未结算') &
                Q(settle_work_order__isnull=True) &
                (
                    Q(marketing_account_id__in=settle_work_order.selected_marketing_account_id_list) |
                    Q(marketing_account__marketing_team_id__in=settle_work_order.selected_marketing_team_id_list)
                )
            )
            if settle_work_order.selected_order_start_dt:
                order_filter_condiction &= Q(order_created_dt__gte=settle_work_order.selected_order_start_dt)
            if settle_work_order.selected_order_end_dt:
                order_filter_condiction &= Q(order_created_dt__lt=settle_work_order.selected_order_end_dt)
            # 根据创建来源模型构建基本查询
            orders = Order.objects.filter(
                order_filter_condiction
            )
            orders_count = orders.count()
            progress_recorder.increment_progress(description=f"正在更新关联订单({orders_count})...")
            orders.update(
                settle_work_order=settle_work_order
            )
            progress_recorder.increment_progress(description="已更新关联订单...")

            # 更新工单的关联状态
            settle_work_order.has_associated_orders = True
            settle_work_order.save(update_fields=['has_associated_orders'])

            return f"已更新关联订单({orders_count})..."
    finally:
        # 确保在出现异常时也会重置处理状态
        with transaction.atomic():
            settle_work_order = SettleWorkOrder.objects.select_for_update().get(pk=settle_work_order_id)
            # settle_work_order.has_associated_orders = requested_has_associated_orders
            settle_work_order.save()


@shared_task(bind=True)
def disassociate_orders_from_settle_work_order(self, settle_work_order_id, requested_has_associated_orders):
    """
    取消订单与结算工单的关联的异步任务

    Args:
        settle_work_order_id: 结算工单ID
    """
    try:
        with transaction.atomic():
            settle_work_order = SettleWorkOrder.objects.select_for_update().get(pk=settle_work_order_id)
            if not self.request.id:
                self.request.id = settle_work_order.task_id.bytes
            settle_work_order.task_id = self.request.id
            settle_work_order.save(update_fields=['task_id'])
            # 记录当前进度
            progress_recorder = ProgressRecorder(self)
        with transaction.atomic():
            settle_work_order = SettleWorkOrder.objects.select_for_update().get(pk=settle_work_order_id)

            # 检查实际的订单关联状态
            has_orders = Order.objects.filter(settle_work_order=settle_work_order).exists()
            if not has_orders:
                return '工单订单关联状态没有变化，无需更新'  # 状态没有变化，无需继续执行任务

            progress_recorder.set_progress(
                current=0, total=3, description="正在查询关联订单..."
            )

            orders = Order.objects.filter(settle_work_order=settle_work_order)
            orders_count = orders.count()

            progress_recorder.increment_progress(description=f"正在取消关联订单({orders_count})...")
            orders.update(settle_work_order=None)

            # 更新工单的关联状态
            settle_work_order.has_associated_orders = False
            settle_work_order.save(update_fields=['has_associated_orders'])

            return f"已取消关联订单({orders_count})..."

    finally:
        # 确保在出现异常时也会重置处理状态
        with transaction.atomic():
            settle_work_order = SettleWorkOrder.objects.select_for_update().get(pk=settle_work_order_id)
            # settle_work_order.has_associated_orders = requested_has_associated_orders
            settle_work_order.save()


@shared_task(bind=True)
def async_delete_selected(self, model_name, queryset_ids, user_id):
    """
    异步删除选中的记录

    Args:
        model_name: 模型名称
        queryset_ids: 要删除的记录ID列表
        user_id: 执行删除操作的用户ID
    """
    from django.apps import apps
    from django.contrib.admin.models import LogEntry, DELETION
    from django.contrib.admin.options import get_content_type_for_model
    from django.contrib.admin.utils import model_ngettext
    from django.utils.translation import gettext_lazy as _

    model = apps.get_model('core', model_name)
    queryset = model.objects.filter(id__in=queryset_ids)
    n = len(queryset)

    if n:
        # 记录删除操作
        LogEntry.objects.log_action(
            user_id=user_id,
            content_type_id=get_content_type_for_model(model).pk,
            object_id=-1,
            object_repr=_("Successfully deleted %(count)d %(items)s.")
                        % {"count": n, "items": model_ngettext(model._meta)},
            action_flag=DELETION,
        )
        # 执行删除
        queryset.delete()

    return f"Successfully deleted {n} {model_ngettext(model._meta)}"


if __name__ == '__main__':
    # import_order_file(21)
    # import_order_filter_file(2)
    import_marketing_team_map_file(20)
    # update_orders_when_approved(5, True)  # 测试结算工单处理
    # associate_orders_to_settle_work_order(79, True)  # 测试关联订单到结算工单
    # disassociate_orders_from_settle_work_order(33)  # 测试取消关联订单到结算工单
