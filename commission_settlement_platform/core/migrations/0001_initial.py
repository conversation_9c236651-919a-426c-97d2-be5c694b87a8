# Generated by Django 5.2.1 on 2025-06-18 14:48

import django.core.validators
import django.db.models.deletion
import django.db.models.expressions
import django.db.models.functions.comparison
import protected_media.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MarketingTeam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('team_name', models.CharField(blank=True, max_length=256, unique=True, verbose_name='营销团队昵称')),
            ],
            options={
                'verbose_name': '营销团队',
                'verbose_name_plural': '营销团队',
            },
        ),
        migrations.CreateModel(
            name='MarketingTeamMapFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('excel_file', protected_media.models.ProtectedFileField(storage=protected_media.models.ProtectedFileSystemStorage(), upload_to='uploads/%Y-%m-%d/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['xlsx'])], verbose_name='Excel 文件')),
                ('task_id', models.UUIDField(blank=True, null=True, verbose_name='任务ID')),
                ('not_empty_row_count', models.PositiveIntegerField(null=True, verbose_name='非空行数')),
                ('success_count', models.PositiveIntegerField(default=0, verbose_name='✅成功数')),
                ('error_count', models.PositiveIntegerField(default=0, verbose_name='❌错误数')),
                ('success_log_file', protected_media.models.ProtectedFileField(blank=True, null=True, storage=protected_media.models.ProtectedFileSystemStorage(), upload_to='uploads/%Y-%m-%d/', verbose_name='成功日志文件')),
                ('error_log_file', protected_media.models.ProtectedFileField(blank=True, null=True, storage=protected_media.models.ProtectedFileSystemStorage(), upload_to='uploads/%Y-%m-%d/', verbose_name='错误日志文件')),
                ('import_completed', models.BooleanField(default=False, verbose_name='导入完成')),
            ],
            options={
                'verbose_name': '营销团队映射文件',
                'verbose_name_plural': '营销团队映射文件',
            },
        ),
        migrations.CreateModel(
            name='OrderFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('excel_file', protected_media.models.ProtectedFileField(storage=protected_media.models.ProtectedFileSystemStorage(), upload_to='uploads/%Y-%m-%d/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['xlsx'])], verbose_name='Excel 文件')),
                ('task_id', models.UUIDField(blank=True, null=True, verbose_name='任务ID')),
                ('not_empty_row_count', models.PositiveIntegerField(null=True, verbose_name='非空行数')),
                ('success_count', models.PositiveIntegerField(default=0, verbose_name='✅成功数')),
                ('error_count', models.PositiveIntegerField(default=0, verbose_name='❌错误数')),
                ('success_log_file', protected_media.models.ProtectedFileField(blank=True, null=True, storage=protected_media.models.ProtectedFileSystemStorage(), upload_to='uploads/%Y-%m-%d/', verbose_name='成功日志文件')),
                ('error_log_file', protected_media.models.ProtectedFileField(blank=True, null=True, storage=protected_media.models.ProtectedFileSystemStorage(), upload_to='uploads/%Y-%m-%d/', verbose_name='错误日志文件')),
                ('import_completed', models.BooleanField(default=False, verbose_name='导入完成')),
            ],
            options={
                'verbose_name': '订单文件',
                'verbose_name_plural': '订单文件',
            },
        ),
        migrations.CreateModel(
            name='OrderFilterFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('excel_file', protected_media.models.ProtectedFileField(storage=protected_media.models.ProtectedFileSystemStorage(), upload_to='uploads/%Y-%m-%d/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['xlsx'])], verbose_name='Excel 文件')),
                ('task_id', models.UUIDField(blank=True, null=True, verbose_name='任务ID')),
                ('not_empty_row_count', models.PositiveIntegerField(null=True, verbose_name='非空行数')),
                ('success_count', models.PositiveIntegerField(default=0, verbose_name='✅成功数')),
                ('error_count', models.PositiveIntegerField(default=0, verbose_name='❌错误数')),
                ('success_log_file', protected_media.models.ProtectedFileField(blank=True, null=True, storage=protected_media.models.ProtectedFileSystemStorage(), upload_to='uploads/%Y-%m-%d/', verbose_name='成功日志文件')),
                ('error_log_file', protected_media.models.ProtectedFileField(blank=True, null=True, storage=protected_media.models.ProtectedFileSystemStorage(), upload_to='uploads/%Y-%m-%d/', verbose_name='错误日志文件')),
                ('import_completed', models.BooleanField(default=False, verbose_name='导入完成')),
            ],
            options={
                'verbose_name': '订单过滤文件',
                'verbose_name_plural': '订单过滤文件',
            },
        ),
        migrations.CreateModel(
            name='MarketingAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_id', models.PositiveBigIntegerField(unique=True, verbose_name='营销帐号ID')),
                ('account_name', models.CharField(blank=True, max_length=256, verbose_name='营销帐号昵称')),
                ('marketing_team', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.marketingteam', verbose_name='营销团队')),
            ],
            options={
                'verbose_name': '营销帐号',
                'verbose_name_plural': '营销帐号',
            },
        ),
        migrations.CreateModel(
            name='SettleWorkOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('work_order_created_dt', models.DateTimeField(auto_now_add=True, verbose_name='工单创建时间')),
                ('has_associated_orders', models.BooleanField(default=False, verbose_name='关联到订单')),
                ('selected_order_start_dt', models.DateTimeField(blank=True, default=None, null=True, verbose_name='已选订单提交起始时间')),
                ('selected_order_end_dt', models.DateTimeField(blank=True, default=None, null=True, verbose_name='已选订单提交结束时间')),
                ('selected_marketing_account_id_list', models.JSONField(default=list, verbose_name='已选营销帐号ID')),
                ('selected_marketing_team_id_list', models.JSONField(default=list, verbose_name='已选营销团队ID')),
                ('approved', models.BooleanField(default=False, verbose_name='已审批(支付)')),
                ('approved_dt', models.DateTimeField(blank=True, null=True, verbose_name='审批(支付)时间')),
                ('task_id', models.UUIDField(blank=True, null=True, verbose_name='任务ID')),
            ],
            options={
                'verbose_name': '结算工单',
                'verbose_name_plural': '结算工单',
                'constraints': [models.CheckConstraint(condition=models.Q(('approved', False), ('has_associated_orders', True), _connector='OR'), name='approved_must_have_associated_orders')],
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_id', models.PositiveBigIntegerField(unique=True, verbose_name='订单编号')),
                ('product_id', models.PositiveBigIntegerField(verbose_name='商品ID')),
                ('product_name', models.CharField(max_length=256, verbose_name='商品名称')),
                ('product_specification', models.CharField(max_length=256, verbose_name='商品规格')),
                ('product_unit_price', models.DecimalField(decimal_places=2, max_digits=9, verbose_name='商品单价')),
                ('deals_count', models.PositiveIntegerField(verbose_name='销售数量')),
                ('order_actual_payment', models.DecimalField(decimal_places=2, max_digits=9, verbose_name='订单应付金额')),
                ('order_created_dt', models.DateTimeField(verbose_name='订单提交时间')),
                ('order_pay_dt', models.DateTimeField(blank=True, null=True, verbose_name='订单支付时间')),
                ('commission_rate', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='佣金率(百分比)')),
                ('order_state', models.CharField(choices=[('', '-'), ('已发货', '已发货'), ('待发货', '待发货'), ('待付款', '待付款'), ('交易成功', '交易成功'), ('交易关闭', '交易关闭'), ('已收货', '已收货')], max_length=256, verbose_name='订单状态')),
                ('order_refunds_state', models.CharField(blank=True, choices=[('', '-'), ('待买家处理', '待买家处理'), ('待买家退货', '待买家退货'), ('待卖家处理', '待卖家处理'), ('待卖家收货', '待卖家收货'), ('待平台判定', '待平台判定'), ('退款成功', '退款成功'), ('退款关闭', '退款关闭'), ('退款执行中', '退款执行中')], max_length=256, verbose_name='订单售后状态')),
                ('settle_rate_percentage', models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='结算比例(百分比 读自表格)')),
                ('settle_rate_decimal', models.GeneratedField(db_persist=True, expression=models.Case(models.When(models.Q(('settle_rate_percentage__isnull', False), ('commission_rate__gt', 1)), then=django.db.models.expressions.CombinedExpression(django.db.models.expressions.CombinedExpression(django.db.models.functions.comparison.Cast(models.F('settle_rate_percentage'), output_field=models.DecimalField(decimal_places=2, max_digits=5)), '-', models.F('commission_rate')), '/', models.Value(100)))), output_field=models.DecimalField(decimal_places=2, max_digits=3), verbose_name='结算比例(小数 计算后)')),
                ('settle_amount', models.GeneratedField(db_persist=True, expression=models.Case(models.When(models.Q(('order_actual_payment__isnull', False), ('settle_rate_percentage__isnull', False)), then=django.db.models.expressions.CombinedExpression(django.db.models.functions.comparison.Cast(models.F('order_actual_payment'), output_field=models.DecimalField(decimal_places=2, max_digits=9)), '*', models.Case(models.When(models.Q(('settle_rate_percentage__isnull', False), ('commission_rate__gt', 1)), then=django.db.models.expressions.CombinedExpression(django.db.models.expressions.CombinedExpression(django.db.models.functions.comparison.Cast(models.F('settle_rate_percentage'), output_field=models.DecimalField(decimal_places=2, max_digits=5)), '-', models.F('commission_rate')), '/', models.Value(100))))))), output_field=models.DecimalField(decimal_places=3, max_digits=10), verbose_name='结算金额')),
                ('approved_marketing_team_name', models.CharField(blank=True, default='', max_length=256, verbose_name='已审批(支付)营销团队昵称')),
                ('settle_state', models.CharField(choices=[('未结算', '❌未审批(支付)'), ('不可结算', '⛔不可审批(支付)'), ('已结算', '✅已审批(支付)')], default='未结算', max_length=256, verbose_name='工单审批状态')),
                ('full_settle_state', models.GeneratedField(choices=[('未结算', '🔴未结算'), ('结算中', '🟡结算中'), ('已结算', '🟢已结算'), ('不可结算', '⛔不可结算'), ('异常结算', '⚠️异常结算')], db_persist=True, expression=models.Case(models.When(models.Q(models.Q(('order_state__in', ['待付款', '交易关闭']), ('order_refunds_state__in', ['待买家处理', '待买家退货', '待卖家处理', '待卖家收货', '待平台判定', '退款成功', '退款执行中']), _connector='OR'), ('settle_state__in', ['未结算'])), then=models.Value('不可结算')), models.When(models.Q(models.Q(('order_state__in', ['待付款', '交易关闭']), ('order_refunds_state__in', ['待买家处理', '待买家退货', '待卖家处理', '待卖家收货', '待平台判定', '退款成功', '退款执行中']), _connector='OR'), ('settle_state__in', ['已结算'])), then=models.Value('异常结算')), models.When(models.Q(('settle_state', '未结算'), ('settle_work_order__isnull', False)), then=models.Value('结算中')), default=models.F('settle_state')), output_field=models.CharField(max_length=256), verbose_name='完整结算状态')),
                ('marketing_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.marketingaccount', verbose_name='营销帐号')),
                ('src_file', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.orderfile', verbose_name='来源文件')),
                ('settle_work_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.settleworkorder', verbose_name='结算工单')),
            ],
            options={
                'verbose_name': '订单',
                'verbose_name_plural': '订单',
                'ordering': ['-order_pay_dt'],
                'indexes': [models.Index(fields=['settle_work_order'], name='settle_work_order_idx'), models.Index(fields=['marketing_account'], name='marketing_account_idx'), models.Index(fields=['order_pay_dt'], name='order_pay_dt_idx'), models.Index(fields=['order_created_dt'], name='order_created_dt_idx')],
            },
        ),
    ]
