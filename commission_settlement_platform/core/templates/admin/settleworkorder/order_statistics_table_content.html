{% load django_htmx %}
<div class="statistics-info-control">
  <div>
    <div class="filter-section">
      <label for="marketing_team_id">营销团队：</label>
      <datalist id="marketing_team_id">
        <select name="marketing_team_id" hx-get="{% url 'admin:settle_work_order_order_statistics' object_id %}"
                hx-trigger="change" hx-target=".order-statistics-table" hx-params="marketing_team_id">
          <option value="" {% if not marketing_team_id %}selected{% endif %}>&mdash;&mdash;&mdash;</option>
          {% for marketing_team in marketing_team_list %}
            <option value="{{ marketing_team.id }}"
                    {% if marketing_team.id|stringformat:"s" == marketing_team_id %}selected{% endif %}>
              {{ marketing_team.team_name }}
            </option>
          {% endfor %}
        </select>
      </datalist>
      <input type="text" name="marketing_team_id" list="marketing_team_id" class="marketing-team-input"
             hx-get="{% url 'admin:settle_work_order_order_statistics' object_id %}" hx-trigger="change"
             hx-target=".order-statistics-table" hx-params="marketing_team_id"
             value="{{ marketing_team_id|default:'' }}"
             placeholder="请选择或输入营销团队">
    </div>
  </div>
  <!-- 添加导出CSV按钮 -->
  <div class="export-btn-group">
    <a href="{% url 'admin:settle_work_order_export_old_system_format_csv' object_id %}
{% if marketing_team_id %}?marketing_team_id={{ marketing_team_id }}{% endif %}"
       class="export-csv-btn"
       title="导出数据为CSV(旧系统格式)">
      导出CSV(旧系统格式)
    </a>
    <a href="{{ export_csv_url }}{% if marketing_team_id %}?marketing_team_id={{ marketing_team_id }}{% endif %}"
       class="export-csv-btn"
       title="导出全部数据为CSV">
      导出CSV
    </a>
  </div>
</div>

<table>
  <thead>
  <tr>
    <th>#</th>
    <th>营销团队</th>
    <th>快手账号ID</th>
    <th>快手账号昵称</th>
    <th>商品ID</th>
    <th>商品名称</th>
    <th>商品规格</th>
    <th>订单提交时间</th>
    <th>商品单价</th>
    <th>销售数量</th>
    <th>订单应付金额</th>
    <th>结算比例(小数)</th>
    <th>结算金额求和</th>
  </tr>
  </thead>
  <tbody>
  {% for item in page_obj %}
    <tr>
      <td>{{ page_obj.start_index|add:forloop.counter0 }}</td>
      <td>
        {% if item.marketing_account__marketing_team_id %}
          <a href="{% url 'admin:core_marketingteam_change' item.marketing_account__marketing_team_id %}"
             target="_blank">
            {{ item.marketing_account__marketing_team__team_name|default:"" }}
          </a>
        {% else %}
          {{ item.marketing_account__marketing_team__team_name|default:"" }}
        {% endif %}
      </td>
      <td>
        {% if item.marketing_account_id %}
          <a href="{% url 'admin:core_marketingaccount_change' item.marketing_account_id %}" target="_blank">
            {{ item.marketing_account__account_id|default:"" }}
          </a>
        {% else %}
          {{ item.marketing_account__account_id|default:"" }}
        {% endif %}
      </td>
      <td>{{ item.marketing_account__account_name|default:"" }}</td>
      <td>{{ item.product_id|default:"" }}</td>
      <td>{{ item.product_name|default:"" }}</td>
      <td>{{ item.product_specification|default:"" }}</td>
      <td>{{ item.order_created_date|date:"Y-m-d"|default:"" }}</td>
      <td>{{ item.product_unit_price|default:""|floatformat:2 }}</td>
      <td>{{ item.deals_count|default:"" }}</td>
      <td>{{ item.order_actual_payment|default:""|floatformat:2 }}</td>
      <td>{{ item.settle_rate_decimal|default:""|floatformat:2 }}</td>
      <td>{{ item.settle_amount|default:""|floatformat:2 }}</td>
    </tr>
  {% empty %}
    <tr>
      <td colspan="13" style="text-align: center;">暂无数据</td>
    </tr>
  {% endfor %}
  </tbody>
  <tfoot>
  <tr class="total-row">
    <td colspan="9"><strong>总计</strong></td>
    <td><strong>{{ statistics.deals_count_sum|default:"" }}</strong></td>
    <td><strong>{{ statistics.order_actual_payment_sum|floatformat:2 }}</strong></td>
    <td></td>
    <td><strong>{{ statistics.settle_amount_sum|floatformat:2 }}</strong></td>
  </tr>
  </tfoot>
</table>

<div class="pagination">
  {% if page_obj.has_previous %}
    <a
      href="{% url 'admin:settle_work_order_order_statistics' object_id %}?page=1{% if marketing_team_id %}&marketing_team_id={{ marketing_team_id }}{% endif %}"
      hx-get="{% url 'admin:settle_work_order_order_statistics' object_id %}?page=1{% if marketing_team_id %}&marketing_team_id={{ marketing_team_id }}{% endif %}"
      hx-target=".order-statistics-table">
      &laquo; 首页
    </a>
    <a href="{% url 'admin:settle_work_order_order_statistics' object_id %}?page={{ page_obj.previous_page_number }}{% if marketing_team_id %}&marketing_team_id={{ marketing_team_id }}{% endif %}"
       hx-get="{% url 'admin:settle_work_order_order_statistics' object_id %}?page={{ page_obj.previous_page_number }}{% if marketing_team_id %}&marketing_team_id={{ marketing_team_id }}{% endif %}"
       hx-target=".order-statistics-table">
      上一页
    </a>
  {% endif %}

  {% for i in page_range %}
    {% if i == page_obj.number %}
      <span class="current-page">{{ i }}</span>
    {% elif i == paginator.ELLIPSIS %}
      <span class="dots">...</span>
    {% else %}
      <a href="{% url 'admin:settle_work_order_order_statistics' object_id %}?page=
          {{ i }}{% if marketing_team_id %}&marketing_team_id={{ marketing_team_id }}{% endif %}"
         hx-get="{% url 'admin:settle_work_order_order_statistics' object_id %}?page=
          {{ i }}{% if marketing_team_id %}&marketing_team_id={{ marketing_team_id }}{% endif %}"
         hx-target=".order-statistics-table">
        {{ i }}
      </a>
    {% endif %}
  {% endfor %}

  {% if page_obj.has_next %}
    <a href="{% url 'admin:settle_work_order_order_statistics' object_id %}?page=
        {{ page_obj.next_page_number }}{% if marketing_team_id %}&marketing_team_id={{ marketing_team_id }}{% endif %}"
       hx-get="{% url 'admin:settle_work_order_order_statistics' object_id %}?page=
        {{ page_obj.next_page_number }}{% if marketing_team_id %}&marketing_team_id={{ marketing_team_id }}{% endif %}"
       hx-target=".order-statistics-table">
      下一页
    </a>
    <a href="{% url 'admin:settle_work_order_order_statistics' object_id %}?page=
      {{ page_obj.paginator.num_pages }}{% if marketing_team_id %}&marketing_team_id={{ marketing_team_id }}{% endif %}"
       hx-get="{% url 'admin:settle_work_order_order_statistics' object_id %}?page=
         {{ page_obj.paginator.num_pages }}{% if marketing_team_id %}&marketing_team_id=
       {{ marketing_team_id }}{% endif %}"
       hx-target=".order-statistics-table">
      末页 &raquo;
    </a>
  {% endif %}

  <span
    class="total-count">第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 条，共 {{ paginator.count }} 条</span>
</div>
