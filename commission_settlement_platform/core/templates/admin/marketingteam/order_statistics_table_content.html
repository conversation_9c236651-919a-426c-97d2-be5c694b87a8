{% load django_htmx %}
<div class="statistics-info-control">
  <div>
    <div class="filter-section">
      <label for="full_settle_state">完整结算状态：</label>
      <select name="full_settle_state"
              hx-get="{% url 'admin:marketing_team_order_statistics' object_id %}"
              hx-trigger="change"
              hx-target=".order-statistics-table"
              hx-params="full_settle_state">
        <option value="" {% if not full_settle_state %}selected{% endif %}>&mdash;&mdash;&mdash;</option>
        {% for value, label in full_settle_states %}
          <option value="{{ value }}" {% if value == full_settle_state %}selected{% endif %}>{{ label }}</option>
        {% endfor %}
      </select>
    </div>
  </div>
  <!-- 添加导出CSV按钮 -->
  <div class="export-btn-group">
    <a href="{{ export_old_system_format_csv_url }}{% if full_settle_state %}?full_settle_state={{ full_settle_state }}{% endif %}"
       class="export-csv-btn"
       title="导出全部数据为CSV（旧系统格式）">
      导出CSV（旧系统格式）
    </a>
    <a href="{{ export_csv_url }}{% if full_settle_state %}?full_settle_state={{ full_settle_state }}{% endif %}"
       class="export-csv-btn"
       title="导出全部数据为CSV">
      导出CSV
    </a>
  </div>
</div>

<table>
  <thead>
  <tr>
    <th>#</th>
    <th>快手账号ID</th>
    <th>营销账号名称</th>
    <th>商品ID</th>
    <th>商品名称</th>
    <th>商品规格</th>
    <th>订单提交时间</th>
    <th>商品单价</th>
    <th>销售数量</th>
    <th>订单应付金额</th>
    <th>结算比例(小数)</th>
    <th>结算金额</th>
  </tr>
  </thead>
  <tbody>
  {% for item in page_obj %}
    <tr>
      <td>{{ page_obj.start_index|add:forloop.counter0 }}</td>
      <td>
        {% if item.marketing_account_id %}
          <a href="{% url 'admin:core_marketingaccount_change' item.marketing_account_id %}" target="_blank">
            {{ item.marketing_account__account_id|default:"" }}
          </a>
        {% else %}
          {{ item.marketing_account__account_id|default:"" }}
        {% endif %}
      </td>
      <td>{{ item.marketing_account__account_name|default:"" }}</td>
      <td>{{ item.product_id|default:"" }}</td>
      <td>{{ item.product_name|default:"" }}</td>
      <td>{{ item.product_specification|default:"" }}</td>
      <td>{{ item.order_created_date|date:"Y-m-d"|default:"" }}</td>
      <td>{{ item.product_unit_price|default:""|floatformat:2 }}</td>
      <td>{{ item.deals_count|default:"" }}</td>
      <td>{{ item.order_actual_payment|default:""|floatformat:2 }}</td>
      <td>{{ item.settle_rate_decimal|default:""|floatformat:2 }}</td>
      <td>{{ item.settle_amount|default:""|floatformat:2 }}</td>
    </tr>
  {% empty %}
    <tr>
      <td colspan="12" style="text-align: center;">暂无数据</td>
    </tr>
  {% endfor %}
  </tbody>
  <tfoot>
  <tr class="total-row">
    <td colspan="9"><strong>总计</strong></td>
    <td><strong>{{ statistics.deals_count_sum|default:"" }}</strong></td>
    <td><strong>{{ statistics.order_actual_payment_sum|floatformat:2 }}</strong></td>
    <td><strong>{{ statistics.settle_amount_sum|floatformat:2 }}</strong></td>
  </tr>
  </tfoot>
</table>

<div class="pagination">
  {% if page_obj.has_previous %}
    <a href="?page={{ page_obj.previous_page_number }}&full_settle_state={{ full_settle_state }}"
       hx-get="{% url 'admin:marketing_team_order_statistics' object_id %}?page={{ page_obj.previous_page_number }}&full_settle_state={{ full_settle_state }}"
       hx-target=".order-statistics-table">&laquo; 上一页</a>
  {% endif %}

  {% for page in page_range %}
    {% if page == page_obj.number %}
      <span class="current-page">{{ page }}</span>
    {% elif page == paginator.ELLIPSIS %}
      <span class="dots">{{ page }}</span>
    {% else %}
      <a href="?page={{ page }}&full_settle_state={{ full_settle_state }}"
         hx-get="{% url 'admin:marketing_team_order_statistics' object_id %}?page={{ page }}&full_settle_state={{ full_settle_state }}"
         hx-target=".order-statistics-table">{{ page }}</a>
    {% endif %}
  {% endfor %}

  {% if page_obj.has_next %}
    <a href="?page={{ page_obj.next_page_number }}&full_settle_state={{ full_settle_state }}"
       hx-get="{% url 'admin:marketing_team_order_statistics' object_id %}?page={{ page_obj.next_page_number }}&full_settle_state={{ full_settle_state }}"
       hx-target=".order-statistics-table">下一页 &raquo;</a>
  {% endif %}

  <span class="total-count">共 {{ paginator.count }} 条记录</span>
</div>
