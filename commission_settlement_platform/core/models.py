import math
from decimal import Decimal

from celery.result import AsyncResult
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator, FileExtensionValidator
from django.db import models, transaction
from django.db.models import Case, When, F, Q, Value, Sum, ExpressionWrapper, Count
from django.db.models.functions import Cast, Round
from django.utils import timezone
from protected_media.models import ProtectedFileField

from commission_settlement_platform.core.utils import find_task_infos_by_name_and_args, revoke_task
from .model_managers import RevokeTaskBeforeDeleteQuerySet


def all_fields(obj, exclude=None):
    if not exclude:
        exclude = []
    fields = obj._meta.get_fields()
    return list(filter(lambda i: i not in exclude, [i.name for i in fields]))


def concrete_fields(obj, exclude=None):
    if not exclude:
        exclude = []
    fields = obj._meta.concrete_fields
    return list(filter(lambda i: i not in exclude, [i.name for i in fields]))


def fields_to_verbose(obj, fields):
    return [obj._meta.get_field(i).verbose_name for i in fields]


class Order(models.Model):
    class Meta:
        verbose_name = '订单'
        verbose_name_plural = '订单'
        ordering = ['-order_pay_dt']
        indexes = [
            models.Index(fields=['settle_work_order'], name='settle_work_order_idx'),
            models.Index(fields=['marketing_account'], name='marketing_account_idx'),
            models.Index(fields=['order_pay_dt'], name='order_pay_dt_idx'),
            models.Index(fields=['order_created_dt'], name='order_created_dt_idx'),
        ]

    SETTLE_STATE_CHOICES = {
        '未结算': '❌未审批(支付)',
        '不可结算': '⛔不可审批(支付)',
        '已结算': '✅已审批(支付)',
    }

    FULL_SETTLE_STATE_CHOICES = {
        '未结算': '🔴未结算',
        '结算中': '🟡结算中',
        '已结算': '🟢已结算',
        '不可结算': '⛔不可结算',  # `can_settle=False` and `settle_state__in=['未结算']`
        '异常结算': '⚠️异常结算'  # `can_settle=False` and `settle_state__in=['已结算']`
    }

    ORDER_STATE_CHOICES = {
        '': '-',
        '已发货': '已发货',
        '待发货': '待发货',
        '待付款': '待付款',
        '交易成功': '交易成功',
        '交易关闭': '交易关闭',
        '已收货': '已收货'
    }

    ORDER_REFUNDS_STATE_CHOICES = {
        '': '-',
        '待买家处理': '待买家处理',
        '待买家退货': '待买家退货',
        '待卖家处理': '待卖家处理',
        '待卖家收货': '待卖家收货',
        '待平台判定': '待平台判定',
        '退款成功': '退款成功',
        '退款关闭': '退款关闭',
        '退款执行中': '退款执行中'
    }

    order_id = models.PositiveBigIntegerField(
        unique=True,
        verbose_name='订单编号'
    )

    product_id = models.PositiveBigIntegerField(
        verbose_name='商品ID'
    )

    product_name = models.CharField(
        max_length=256,
        verbose_name='商品名称'
    )

    product_specification = models.CharField(
        max_length=256,
        verbose_name='商品规格'
    )

    product_unit_price = models.DecimalField(
        max_digits=9,
        decimal_places=2,
        verbose_name='商品单价'
    )

    deals_count = models.PositiveIntegerField(
        verbose_name='销售数量'
    )

    order_actual_payment = models.DecimalField(
        max_digits=9,
        decimal_places=2,
        verbose_name='订单应付金额'
    )

    order_created_dt = models.DateTimeField(
        verbose_name='订单提交时间'
    )

    order_pay_dt = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='订单支付时间'
    )

    commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name='佣金率(百分比)'
    )  # 这个字段要参与运算了，佣金率 > 1% 的，settle_rate_decimal = settle_rate_percentage - commission_rate

    order_state = models.CharField(
        max_length=256,
        choices=ORDER_STATE_CHOICES,
        verbose_name='订单状态'
    )

    order_refunds_state = models.CharField(
        max_length=256,
        blank=True,
        choices=ORDER_REFUNDS_STATE_CHOICES,
        verbose_name='订单售后状态'
    )

    settle_rate_percentage = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name='结算比例(百分比 读自表格)',
    )

    settle_rate_decimal = models.GeneratedField(
        expression=Case(
            When(
                (
                    Q(settle_rate_percentage__isnull=False) &
                    Q(commission_rate__gt=1)
                ),
                then=(
                    (
                        Cast(F('settle_rate_percentage'), output_field=models.DecimalField(
                            max_digits=5,
                            decimal_places=2,
                        )) - F('commission_rate')
                    ) / 100
                )
            )
        ),
        output_field=models.DecimalField(
            max_digits=3,
            decimal_places=2,
        ),
        db_persist=True,
        verbose_name='结算比例(小数 计算后)'
    )

    settle_amount = models.GeneratedField(
        expression=Case(
            When(
                (
                    Q(order_actual_payment__isnull=False) &
                    Q(settle_rate_percentage__isnull=False)
                ),
                then=Cast(F('order_actual_payment'), output_field=models.DecimalField(
                    max_digits=9,
                    decimal_places=2,
                )) * Case(
                    When(
                        (
                            Q(settle_rate_percentage__isnull=False) &
                            Q(commission_rate__gt=1)
                        ),
                        then=(
                            (
                                Cast(F('settle_rate_percentage'), output_field=models.DecimalField(
                                    max_digits=5,
                                    decimal_places=2,
                                )) - F('commission_rate')
                            ) / 100
                        )
                    )
                )
                # then=F('order_actual_payment') * F('settle_rate_decimal')
                # 这里不用这个计算，是因为不能依赖 GeneratedField 进行计算
            )
        ),
        output_field=models.DecimalField(
            max_digits=10,
            decimal_places=3,
        ),
        db_persist=True,
        verbose_name='结算金额'
    )

    marketing_account = models.ForeignKey(
        to='MarketingAccount',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        verbose_name='营销帐号',
        db_index=True,
    )

    approved_marketing_team_name = models.CharField(
        max_length=256,
        blank=True,
        default='',
        verbose_name='已审批(支付)营销团队昵称'
    )

    settle_state = models.CharField(
        max_length=256,
        choices=SETTLE_STATE_CHOICES,
        default='未结算',
        verbose_name='工单审批状态'
    )

    full_settle_state = models.GeneratedField(
        expression=Case(
            When(
                Q(
                    Q(order_state__in=[
                        '待付款',
                        '交易关闭',
                    ]) |
                    Q(order_refunds_state__in=[
                        '待买家处理',
                        '待买家退货',
                        '待卖家处理',
                        '待卖家收货',
                        '待平台判定',
                        '退款成功',
                        '退款执行中',
                    ])
                ) & Q(settle_state__in=['未结算']),
                then=Value('不可结算')
            ),
            When(
                Q(
                    Q(order_state__in=[
                        '待付款',
                        '交易关闭',
                    ]) |
                    Q(order_refunds_state__in=[
                        '待买家处理',
                        '待买家退货',
                        '待卖家处理',
                        '待卖家收货',
                        '待平台判定',
                        '退款成功',
                        '退款执行中',
                    ])
                ) & Q(settle_state__in=['已结算']),
                then=Value('异常结算')
            ),
            When(
                Q(settle_state='未结算') & Q(settle_work_order__isnull=False),
                # 有结算工单的，则变成结算中
                then=Value('结算中')
            ),
            default=F('settle_state')
        ),
        output_field=models.CharField(
            max_length=256,
        ),
        choices=FULL_SETTLE_STATE_CHOICES,
        db_persist=True,
        verbose_name='完整结算状态'
    )

    settle_work_order = models.ForeignKey(
        to='SettleWorkOrder',
        blank=True,
        null=True,
        on_delete=models.PROTECT,
        verbose_name='结算工单'
    )

    src_file = models.ForeignKey(
        to='OrderFile',
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        verbose_name='来源文件'
    )

    @classmethod
    def export_resource_classes(cls):
        from commission_settlement_platform.core.import_export_resources import OrderResource
        return {
            '订单': ('订单资源', OrderResource),
        }

    def mark_can_not_settle_order(self):
        if (
            self.order_state in ['待付款',
                                 '交易关闭', ] or
            self.order_refunds_state in ['待买家处理',
                                         '待买家退货',
                                         '待卖家处理',
                                         '待卖家收货',
                                         '待平台判定',
                                         '退款成功',
                                         '退款执行中', ]
        ):
            if self.settle_state == '未结算':
                self.settle_state = '不可结算'

    def pre_save(self):
        self.mark_can_not_settle_order()

    def save(self, *args, **kwargs):
        self.pre_save()
        self.full_clean()
        return super().save(*args, **kwargs)

    def __str__(self):
        return str(self.order_id)


class MarketingAccount(models.Model):
    class Meta:
        verbose_name = '营销帐号'
        verbose_name_plural = '营销帐号'

    account_id = models.PositiveBigIntegerField(
        unique=True,
        verbose_name='营销帐号ID'
    )

    account_name = models.CharField(
        max_length=256,
        blank=True,
        verbose_name='营销帐号昵称'
    )

    marketing_team = models.ForeignKey(
        to='MarketingTeam',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        verbose_name='营销团队'
    )

    @staticmethod
    def autocomplete_search_fields():
        return "account_id__iexact", "account_name__icontains",

    @staticmethod
    def contribute_order_statistics(queryset=None, *args, **kwargs):
        return queryset.annotate(
            unsettle_amount=Sum(
                F('order__settle_amount'),
                filter=Q(order__full_settle_state='未结算'),
                default=0
            ),
            settling_amount=Sum(
                F('order__settle_amount'),
                filter=Q(order__full_settle_state='结算中'),
                default=0
            ),
            settled_amount=Sum(
                F('order__settle_amount'),
                filter=Q(order__full_settle_state='已结算'),
                default=0
            ),
            can_not_settle_amount=Sum(
                F('order__settle_amount'),
                filter=Q(order__full_settle_state='不可结算'),
                default=0
            ),
            abnormal_settle_amount=Sum(
                F('order__settle_amount'),
                filter=Q(order__full_settle_state='异常结算'),
                default=0
            ),
            approved_settle_amount=Sum(
                F('order__settle_amount'),
                filter=Q(order__settle_state='已结算'),
                default=0
            ),
            abnormal_settle_amount_rate=ExpressionWrapper(
                Case(
                    When(
                        Q(abnormal_settle_amount__gt=0),
                        then=Round(
                            F('abnormal_settle_amount') / F('approved_settle_amount'), 3
                        )
                    ),
                    default=Decimal(0.000),
                ),
                output_field=models.DecimalField(
                    default=Decimal(0.000),
                    max_digits=6,
                    decimal_places=3,
                )
            ),
        ).order_by('-unsettle_amount', '-settling_amount', '-settled_amount')

    @classmethod
    def export_resource_classes(cls):
        from commission_settlement_platform.core.import_export_resources import MarketingAccountResource
        return {
            '营销团队': ('营销团队资源', MarketingAccountResource),
        }

    def related_label(self):
        return f'{self.account_name}({self.account_id})'

    def __str__(self):
        return f'{self.account_id}'


class MarketingTeam(models.Model):
    class Meta:
        verbose_name = '营销团队'
        verbose_name_plural = '营销团队'

    team_name = models.CharField(
        unique=True,
        max_length=256,
        blank=True,
        verbose_name='营销团队昵称'
    )

    @staticmethod
    def autocomplete_search_fields():
        return "team_name__iexact", "team_name__icontains",

    @staticmethod
    def contribute_order_statistics(queryset):
        return queryset.annotate(
            unsettle_amount=Sum(
                F('marketingaccount__order__settle_amount'),
                filter=Q(marketingaccount__order__full_settle_state='未结算'),
                default=0
            ),
            settling_amount=Sum(
                F('marketingaccount__order__settle_amount'),
                filter=Q(marketingaccount__order__full_settle_state='结算中'),
                default=0
            ),
            settled_amount=Sum(
                F('marketingaccount__order__settle_amount'),
                filter=Q(marketingaccount__order__full_settle_state='已结算'),
                default=0
            ),
            can_not_settle_amount=Sum(
                F('marketingaccount__order__settle_amount'),
                filter=Q(marketingaccount__order__full_settle_state='不可结算'),
                default=0
            ),
            abnormal_settle_amount=Sum(
                F('marketingaccount__order__settle_amount'),
                filter=Q(marketingaccount__order__full_settle_state='异常结算'),
                default=0
            ),
            approved_settle_amount=Sum(
                F('marketingaccount__order__settle_amount'),
                filter=Q(marketingaccount__order__settle_state='已结算'),
                default=0
            ),
            abnormal_settle_amount_rate=ExpressionWrapper(
                Case(
                    When(
                        Q(abnormal_settle_amount__gt=0),
                        then=Round(
                            F('abnormal_settle_amount') / F('approved_settle_amount'), 3
                        )
                    ),
                    default=Decimal(0.000),
                ),
                output_field=models.DecimalField(
                    default=Decimal(0.000),
                    max_digits=6,
                    decimal_places=3,
                )
            ),
        ).order_by('-unsettle_amount', '-settling_amount', '-settled_amount')

    def __str__(self):
        return self.team_name


class SettleWorkOrder(models.Model):
    class Meta:
        verbose_name = '结算工单'
        verbose_name_plural = '结算工单'
        constraints = [
            models.CheckConstraint(
                check=models.Q(approved=False) | models.Q(has_associated_orders=True),
                name='approved_must_have_associated_orders'
            )
        ]

    objects = RevokeTaskBeforeDeleteQuerySet.as_manager()

    work_order_created_dt = models.DateTimeField(
        auto_now_add=True,
        verbose_name='工单创建时间'
    )
    has_associated_orders = models.BooleanField(
        default=False,
        verbose_name='关联到订单'
    )
    # 这里不确定是用订单提交时间还是支付时间做，暂定用订单提交时间
    selected_order_start_dt = models.DateTimeField(
        blank=True,
        null=True,
        default=None,
        verbose_name='已选订单提交起始时间'
    )
    selected_order_end_dt = models.DateTimeField(
        blank=True,
        null=True,
        default=None,
        verbose_name='已选订单提交结束时间'
    )
    selected_marketing_account_id_list = models.JSONField(
        default=list,
        verbose_name='已选营销帐号ID'
    )
    selected_marketing_team_id_list = models.JSONField(
        default=list,
        verbose_name='已选营销团队ID'
    )
    approved = models.BooleanField(
        default=False,
        verbose_name='已审批(支付)'
    )
    approved_dt = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='审批(支付)时间'
    )
    task_id = models.UUIDField(
        blank=True,
        null=True,
        verbose_name='任务ID'
    )

    @property
    def processing(self):
        """
        通过检查 task_id 对应的 Celery 任务状态来判断是否在处理中
        """
        if not self.task_id:
            return False
        task = AsyncResult(str(self.task_id))
        parent_processing = task.state in [
            'PROGRESS',
            'STARTED',
            'RETRY'
        ]
        children_processing = False
        if task.children:
            children_processing = all([c.state in [
                'PROGRESS',
                'STARTED',
                'RETRY'
            ] for c in task.children])
        return children_processing or parent_processing

    @staticmethod
    def contribute_order_statistics(queryset):
        return queryset.annotate(
            deals_count_sum=Sum('order__deals_count'),
            order_actual_payment_sum=Sum('order__order_actual_payment'),
            settle_amount_sum=Sum('order__settle_amount'),
            order_count=Count('order', distinct=True),
            marketing_account_count=Count('order__marketing_account', distinct=True),
            marketing_team_count=Count('order__marketing_account__marketing_team', distinct=True),
            settling_amount=Sum(
                F('order__settle_amount'),
                filter=Q(order__full_settle_state='结算中'),
                default=0
            ),
            settled_amount=Sum(
                F('order__settle_amount'),
                filter=Q(order__full_settle_state='已结算'),
                default=0
            ),
            can_not_settle_amount=Sum(
                F('order__settle_amount'),
                filter=Q(order__full_settle_state='不可结算'),
                default=0
            ),
            abnormal_settle_amount=Sum(
                F('order__settle_amount'),
                filter=Q(order__full_settle_state='异常结算'),
                default=0
            ),
            approved_settle_amount=Sum(
                F('order__settle_amount'),
                filter=Q(order__settle_state='已结算'),
                default=0
            ),
            abnormal_settle_amount_rate=ExpressionWrapper(
                Case(
                    When(
                        Q(abnormal_settle_amount__gt=0),
                        then=Round(
                            F('abnormal_settle_amount') / F('approved_settle_amount'), 3
                        )
                    ),
                    default=Decimal(0.000),
                ),
                output_field=models.DecimalField(
                    default=Decimal(0.000),
                    max_digits=6,
                    decimal_places=3,
                )
            ),
        )

    def save(
        self,
        force_insert=False,
        force_update=False,
        using=None,
        update_fields=None,
        tasks=None,
    ):
        if tasks:
            with transaction.atomic():
                if self.processing:
                    raise ValidationError('当前工单正在执行任务中，请稍后再试')

                async_result = tasks.apply_async()
                self.task_id = async_result.id
                if not update_fields:
                    update_fields = []
                update_fields = (set(update_fields) - {'has_associated_orders', 'approved'}) or None
                super().save(
                    force_insert=force_insert,
                    force_update=force_update,
                    using=using,
                    update_fields=update_fields,
                )
                return
        super().save(
            force_insert=force_insert,
            force_update=force_update,
            using=using,
            update_fields=update_fields,
        )

    def delete(self, using=None, keep_parents=False):
        if self.has_associated_orders:
            raise ValidationError('无法删除已关联订单的结算工单')
        if self.processing:
            raise ValidationError('无法删除正在执行任务的结算工单')
        if self.order_set.filter(full_settle_state='已结算').exists():
            raise ValidationError('无法删除结算工单，因为它包含"🟢已结算"的订单')
        return super().delete(using=using, keep_parents=keep_parents)

    def __str__(self):
        return self.work_order_created_dt.astimezone(
            timezone.get_current_timezone()
        ).strftime("%Y-%m-%d %H:%M:%S")

    def clean(self):
        if self.approved and not self.has_associated_orders:
            raise ValidationError('已审批(支付)的工单必须关联订单')
        super().clean()


class ExcelFileABC(models.Model):
    class Meta:
        abstract = True

    objects = RevokeTaskBeforeDeleteQuerySet.as_manager()

    @staticmethod
    def autocomplete_search_fields():
        return "excel_file__iexact", "excel_file__icontains",

    excel_file = ProtectedFileField(
        upload_to="uploads/%Y-%m-%d/",
        validators=[FileExtensionValidator(allowed_extensions=['xlsx'])],
        verbose_name='Excel 文件'
    )

    task_id = models.UUIDField(
        blank=True,
        null=True,
        verbose_name='任务ID'
    )

    not_empty_row_count = models.PositiveIntegerField(
        null=True,
        verbose_name='非空行数',
    )

    success_count = models.PositiveIntegerField(
        default=0,
        verbose_name='✅成功数',
    )

    error_count = models.PositiveIntegerField(
        default=0,
        verbose_name='❌错误数',
    )

    success_log_file = ProtectedFileField(
        upload_to="uploads/%Y-%m-%d/",
        blank=True,
        null=True,
        verbose_name='成功日志文件'
    )

    error_log_file = ProtectedFileField(
        upload_to="uploads/%Y-%m-%d/",
        blank=True,
        null=True,
        verbose_name='错误日志文件'
    )

    import_completed = models.BooleanField(
        default=False,
        verbose_name='导入完成'
    )

    @property
    def excel_file_size(self):
        if not self.excel_file:
            return '0B'

        try:
            file_size = self.excel_file.size
            if file_size == 0:
                return '0B'  # 防止 math.log(0,1024) 抛出 ValueError

            unit_list = ['B', 'KB', 'MB', 'GB']
            exponent = int(math.log(file_size, 1024))
            return f'{file_size / 1024 ** exponent:.2f}{unit_list[exponent]}'
        except Exception:
            return '-'

    @property
    def processing(self):
        """
        通过检查 task_id 对应的 Celery 任务状态来判断是否在处理中
        """
        if not self.task_id:
            return False
        task = AsyncResult(str(self.task_id))
        return task.state in [
            # 'PENDING',
            'PROGRESS',
            'STARTED',
            'RETRY'
        ]

    def save(
        self,
        force_insert=False,
        force_update=False,
        using=None,
        update_fields=None,
        apply_task=True,
    ):
        super().save(
            force_insert=force_insert,
            force_update=force_update,
            using=using,
            update_fields=update_fields,
        )
        if apply_task and not self.import_completed:
            from importlib import import_module
            task_module = import_module(f'commission_settlement_platform.core.tasks')
            task_function = getattr(task_module, self.task_name)
            old_task_infos = find_task_infos_by_name_and_args(task_name=task_function.name, args=[self.pk])
            for old_task_info in old_task_infos:
                revoke_task(old_task_info['id'])
            task = task_function.apply_async(
                args=[self.pk],
            )
            self.task_id = task.id
            super().save(
                force_insert=force_insert,
                force_update=force_update,
                using=using,
                update_fields=update_fields
            )

    def delete(self, using=None, keep_parents=False):
        revoke_task(str(self.task_id))
        return super().delete(using, keep_parents)

    def __str__(self):
        return self.excel_file.name


class OrderFile(ExcelFileABC):
    class Meta:
        verbose_name = '订单文件'
        verbose_name_plural = '订单文件'

    task_name = 'import_order_file'


class OrderFilterFile(ExcelFileABC):
    class Meta:
        verbose_name = '订单过滤文件'
        verbose_name_plural = '订单过滤文件'

    task_name = 'import_order_filter_file'


class MarketingTeamMapFile(ExcelFileABC):
    class Meta:
        verbose_name = '营销团队映射文件'
        verbose_name_plural = '营销团队映射文件'

    task_name = 'import_marketing_team_map_file'
