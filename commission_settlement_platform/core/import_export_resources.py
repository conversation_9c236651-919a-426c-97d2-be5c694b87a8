from urllib.parse import quote

from django.utils import timezone
from import_export import resources
from import_export.fields import Field
from import_export.widgets import ForeignKeyWidget

from commission_settlement_platform.core.models import Order, MarketingAccount, MarketingTeam


class OrderResource(resources.ModelResource):
    # 显式声明字段，使用 verbose_name 作为列名
    order_id = resources.Field(attribute='order_id', column_name='订单编号')
    product_id = resources.Field(attribute='product_id', column_name='商品ID')
    product_name = resources.Field(attribute='product_name', column_name='商品名称')
    product_specification = resources.Field(attribute='product_specification', column_name='商品规格')
    product_unit_price = resources.Field(attribute='product_unit_price', column_name='商品单价')
    deals_count = resources.Field(attribute='deals_count', column_name='销售数量')
    order_actual_payment = resources.Field(attribute='order_actual_payment', column_name='订单应付金额')
    order_created_dt = resources.Field(attribute='order_created_dt', column_name='订单提交时间')
    order_pay_dt = resources.Field(attribute='order_pay_dt', column_name='订单支付时间')
    commission_rate = resources.Field(attribute='commission_rate', column_name='佣金率')
    order_state = resources.Field(attribute='order_state', column_name='订单状态')
    order_refunds_state = resources.Field(attribute='order_refunds_state', column_name='订单售后状态')
    settle_rate_percentage = resources.Field(attribute='settle_rate_percentage', column_name='结算比例(百分比 读自表格)')
    settle_rate_decimal = resources.Field(attribute='settle_rate_decimal', column_name='结算比例(小数 计算后)')
    settle_amount = resources.Field(attribute='settle_amount', column_name='结算金额')
    marketing_account = resources.Field(attribute='marketing_account', column_name='营销帐号')
    approved_marketing_team_name = resources.Field(
        attribute='approved_marketing_team_name',
        column_name='已审批(支付)营销团队昵称'
    )
    settle_state = resources.Field(attribute='settle_state', column_name='工单审批状态')
    full_settle_state = resources.Field(attribute='full_settle_state', column_name='完整结算状态')
    settle_work_order = resources.Field(attribute='settle_work_order', column_name='结算工单')
    src_file = resources.Field(attribute='src_file', column_name='源文件')

    class Meta:
        model = Order
        # 设置导出顺序
        export_order = (
            'id', 'order_id', 'product_id', 'product_name', 'product_specification',
            'product_unit_price', 'deals_count', 'order_actual_payment',
            'order_created_dt', 'order_pay_dt', 'commission_rate',
            'order_state', 'order_refunds_state', 'settle_rate_percentage',
            'settle_rate_decimal', 'settle_amount', 'marketing_account',
            'approved_marketing_team_name', 'settle_state', 'full_settle_state',
            'settle_work_order', 'src_file'
        )

    def dehydrate_product_id(self, order):
        return str(order.product_id)

    def dehydrate_order_id(self, order):
        return str(order.order_id)

    def dehydrate_order_created_dt(self, order):
        if order.order_created_dt:
            return order.order_created_dt.astimezone(timezone.get_current_timezone()).strftime('%Y-%m-%d %H:%M:%S')
        return ''

    def dehydrate_order_pay_dt(self, order):
        if order.order_pay_dt:
            return order.order_pay_dt.astimezone(timezone.get_current_timezone()).strftime('%Y-%m-%d %H:%M:%S')
        return ''

    def get_export_filename(self, request, queryset, file_format):
        timestamp = timezone.now().astimezone(timezone.get_current_timezone()).strftime('%Y%m%d%H%M%S')
        filename = f"订单导出_{timestamp}.{file_format.get_extension()}"
        return quote(filename)


class MarketingAccountResource(resources.ModelResource):
    # 显式声明字段，使用 verbose_name 作为列名
    account_id = resources.Field(attribute='account_id', column_name='营销帐号ID')
    account_name = resources.Field(attribute='account_name', column_name='营销帐号昵称')
    team_name = Field(
        attribute='marketing_team',
        column_name='营销团队昵称',
        widget=ForeignKeyWidget(
            MarketingTeam,
            field='team_name'
        )
    )

    class Meta:
        model = MarketingAccount
        # 设置导出顺序
        fields = ('account_id', 'account_name', 'team_name')
        export_order = ('account_id', 'account_name', 'team_name')

    def dehydrate_account_id(self, marketing_account):
        return str(marketing_account.account_id)

    def get_export_filename(self, request, queryset, file_format):
        timestamp = timezone.now().astimezone(timezone.get_current_timezone()).strftime('%Y%m%d%H%M%S')
        filename = f"营销帐号导出_{timestamp}.{file_format.get_extension()}"
        return quote(filename)
